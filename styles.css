/* CSS Custom Properties */
:root {
    --primary-color: #8B4513;
    --secondary-color: #D2691E;
    --accent-color: #F4A460;
    --text-dark: #2C1810;
    --text-light: #6B4423;
    --background-light: #FFF8F0;
    --background-white: #FFFFFF;
    --border-color: #E8D5C4;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
    --font-primary: 'Georgia', serif;
    --font-secondary: 'Arial', sans-serif;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--background-white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4 {
    font-family: var(--font-primary);
    font-weight: normal;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

/* Header */
.header {
    background: var(--background-white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    position: relative;
}

.cart-btn:hover {
    background: var(--secondary-color);
}

.cart-count {
    background: var(--accent-color);
    color: var(--text-dark);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.mobile-menu-btn {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    gap: 3px;
}

.mobile-menu-btn span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
    padding: 4rem 0;
    display: flex;
    align-items: center;
    min-height: 70vh;
}

.hero-content {
    flex: 1;
    padding: 0 20px;
    max-width: 600px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.cta-button {
    background: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: bold;
    display: inline-block;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.cta-button:hover {
    background: var(--secondary-color);
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.secondary-button {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: bold;
    display: inline-block;
    transition: var(--transition);
}

.secondary-button:hover {
    background: var(--primary-color);
    color: white;
}

/* Hero Candle Display */
.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.candle-display {
    position: relative;
    width: 300px;
    height: 300px;
}

.candle {
    position: absolute;
    width: 80px;
    height: 120px;
    border-radius: 8px 8px 4px 4px;
    box-shadow: var(--shadow);
    animation: float 3s ease-in-out infinite;
}

.candle-1 {
    background: linear-gradient(to bottom, var(--accent-color), var(--secondary-color));
    top: 20px;
    left: 50px;
    animation-delay: 0s;
}

.candle-2 {
    background: linear-gradient(to bottom, var(--secondary-color), var(--primary-color));
    top: 60px;
    right: 40px;
    animation-delay: 1s;
}

.candle-3 {
    background: linear-gradient(to bottom, var(--primary-color), #654321);
    bottom: 40px;
    left: 80px;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Sections */
.section-title {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--accent-color);
}

.featured-products, .categories, .about {
    padding: 4rem 0;
}

.featured-products {
    background: var(--background-light);
}

/* Product Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background: var(--background-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    cursor: pointer;
}

.product-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-5px);
}

.product-image {
    width: 100%;
    height: 250px;
    background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-family: var(--font-primary);
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.product-price {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.add-to-cart-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: bold;
    transition: var(--transition);
}

.add-to-cart-btn:hover {
    background: var(--secondary-color);
}

.text-center {
    text-align: center;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    background: var(--background-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    cursor: pointer;
}

.category-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-5px);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.category-card h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.category-card p {
    color: var(--text-light);
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.about-text p {
    color: var(--text-light);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.features-list {
    list-style: none;
}

.features-list li {
    padding: 0.5rem 0;
    color: var(--text-dark);
    position: relative;
    padding-left: 2rem;
}

.features-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.craft-illustration {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: var(--shadow-hover);
}

.craft-illustration::before {
    content: '🕯️';
    font-size: 4rem;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--background-light);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--background-light);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--accent-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    font-size: 1.5rem;
    transition: var(--transition);
}

.footer-bottom {
    border-top: 1px solid var(--text-light);
    padding-top: 1rem;
    text-align: center;
    color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 2rem 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-image {
        order: -1;
    }

    .candle-display {
        width: 200px;
        height: 200px;
    }

    .candle {
        width: 60px;
        height: 90px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
    padding: 3rem 0;
    text-align: center;
}

.page-header h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.page-header p {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Filters Section */
.filters-section {
    background: var(--background-white);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.filters-container {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    align-items: end;
    margin-bottom: 1rem;
}

.search-filter {
    display: flex;
    flex-direction: column;
}

.search-input {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: var(--text-dark);
}

.filter-select {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.results-info {
    color: var(--text-light);
    font-style: italic;
}

/* Products Section */
.products-section {
    padding: 3rem 0;
}

.no-products {
    text-align: center;
    color: var(--text-light);
    font-size: 1.2rem;
    padding: 3rem;
    grid-column: 1 / -1;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 3rem;
}

.pagination-btn {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    background: white;
    color: var(--text-dark);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: bold;
}

.pagination-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Active navigation link */
.nav-link.active {
    color: var(--primary-color);
    font-weight: bold;
}

/* Mobile menu styles */
@media (max-width: 768px) {
    .nav-menu.active {
        display: flex;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--background-white);
        flex-direction: column;
        padding: 1rem;
        box-shadow: var(--shadow);
        z-index: 1000;
    }

    .mobile-menu-btn.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .mobile-menu-btn.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-btn.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .filters-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .pagination {
        flex-wrap: wrap;
    }
}

/* Product Detail Styles */
.product-detail-section {
    padding: 3rem 0;
}

.product-detail-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.product-detail-image {
    position: sticky;
    top: 100px;
}

.product-image-large {
    width: 100%;
    height: 500px;
    background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-hover);
}

.product-detail-info h1 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.product-detail-price {
    font-size: 2rem;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

.product-detail-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.product-details,
.product-ingredients {
    margin-bottom: 2rem;
}

.product-details h3,
.product-ingredients h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 0.5rem;
}

.product-details ul,
.product-ingredients ul {
    list-style: none;
    padding-left: 0;
}

.product-details li,
.product-ingredients li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.product-details li:last-child,
.product-ingredients li:last-child {
    border-bottom: none;
}

.product-actions {
    background: var(--background-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.quantity-selector {
    margin-bottom: 1.5rem;
}

.quantity-selector label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: var(--text-dark);
}

.quantity-selector input {
    width: 80px;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.add-to-cart-btn.large {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: bold;
}

.back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: bold;
    transition: var(--transition);
}

.back-link:hover {
    color: var(--secondary-color);
}

.related-products {
    background: var(--background-light);
    padding: 4rem 0;
}

/* Cart Message */
.cart-message {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 1001;
    animation: slideIn 0.3s ease-out;
}

.cart-message-content {
    background: var(--primary-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-message-content button {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Product Detail */
@media (max-width: 768px) {
    .product-detail-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .product-detail-image {
        position: static;
    }

    .product-image-large {
        height: 300px;
    }

    .product-detail-info h1 {
        font-size: 2rem;
    }

    .product-detail-price {
        font-size: 1.5rem;
    }

    .cart-message {
        right: 10px;
        left: 10px;
    }
}

/* Cart Page Styles */
.cart-section {
    padding: 3rem 0;
    min-height: 60vh;
}

.cart-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.cart-items-section {
    background: var(--background-white);
}

.cart-item {
    display: grid;
    grid-template-columns: 100px 1fr auto auto auto;
    gap: 1rem;
    align-items: center;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    background: var(--background-white);
    box-shadow: var(--shadow);
}

.cart-item-image {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
    border-radius: var(--border-radius);
}

.cart-item-details h3 {
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.cart-item-price {
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.cart-item-category {
    color: var(--text-light);
    font-size: 0.9rem;
    text-transform: capitalize;
}

.cart-item-quantity label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-dark);
}

.cart-item-quantity input {
    width: 60px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-align: center;
}

.cart-item-subtotal {
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--primary-color);
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    transition: var(--transition);
}

.remove-btn:hover {
    background: #c82333;
}

.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--background-light);
    border-radius: var(--border-radius);
}

.empty-cart h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.empty-cart p {
    color: var(--text-light);
    margin-bottom: 2rem;
}

/* Cart Summary */
.cart-summary {
    background: var(--background-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    position: sticky;
    top: 100px;
}

.cart-summary h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.summary-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
}

.summary-line.total {
    border-top: 2px solid var(--primary-color);
    padding-top: 1rem;
    font-weight: bold;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.free-shipping {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    text-align: center;
    margin: 1rem 0;
    font-weight: bold;
}

.shipping-notice {
    background: var(--accent-color);
    color: var(--text-dark);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    text-align: center;
    margin: 1rem 0;
    font-size: 0.9rem;
}

.checkout-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 1rem;
}

.checkout-btn:hover {
    background: var(--secondary-color);
}

.clear-cart-btn {
    width: 100%;
    background: transparent;
    color: var(--text-light);
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.clear-cart-btn:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.continue-shopping {
    padding: 2rem 0;
    background: var(--background-light);
}

.cart-btn.active {
    background: var(--secondary-color);
}

/* Responsive Cart */
@media (max-width: 768px) {
    .cart-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .cart-item {
        grid-template-columns: 80px 1fr;
        gap: 1rem;
    }

    .cart-item-quantity,
    .cart-item-subtotal,
    .cart-item-remove {
        grid-column: 1 / -1;
        justify-self: start;
    }

    .cart-item-quantity {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .cart-item-quantity label {
        margin-bottom: 0;
    }

    .cart-summary {
        position: static;
    }
}

/* Checkout Page Styles */
.checkout-section {
    padding: 3rem 0;
}

.checkout-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.checkout-form {
    background: var(--background-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.form-section {
    margin-bottom: 2.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: var(--text-dark);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.shipping-options,
.payment-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.shipping-option,
.payment-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.shipping-option:hover,
.payment-option:hover {
    border-color: var(--primary-color);
}

.shipping-option input,
.payment-option input {
    margin-right: 1rem;
    width: auto;
}

.shipping-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.shipping-name {
    font-weight: bold;
    color: var(--text-dark);
}

.shipping-time {
    color: var(--text-light);
    font-size: 0.9rem;
}

.shipping-price {
    font-weight: bold;
    color: var(--primary-color);
}

.payment-option span {
    font-weight: bold;
    color: var(--text-dark);
}

.credit-card-fields {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.place-order-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1.25rem;
    border-radius: var(--border-radius);
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 2rem;
}

.place-order-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.checkout-summary {
    background: var(--background-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    position: sticky;
    top: 100px;
}

.checkout-items {
    margin-bottom: 1.5rem;
}

.checkout-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.checkout-item:last-child {
    border-bottom: none;
}

.checkout-totals {
    border-top: 2px solid var(--primary-color);
    padding-top: 1rem;
}

.checkout-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.checkout-line.total {
    font-weight: bold;
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Responsive Checkout */
@media (max-width: 768px) {
    .checkout-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .checkout-summary {
        position: static;
    }

    .shipping-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Confirmation Page Styles */
.confirmation-section {
    padding: 4rem 0;
    min-height: 70vh;
}

.confirmation-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.confirmation-header {
    margin-bottom: 3rem;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.confirmation-header h1 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.confirmation-header p {
    color: var(--text-light);
    font-size: 1.2rem;
}

.order-details {
    background: var(--background-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 3rem;
    text-align: left;
}

.order-summary h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.order-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--background-white);
    border-radius: var(--border-radius);
}

.order-number,
.order-date,
.order-total {
    padding: 0.5rem;
}

.order-total {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.order-items h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 0.5rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-white);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
}

.item-image {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--accent-color), var(--secondary-color));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: bold;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.item-quantity {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.item-price {
    color: var(--primary-color);
    font-weight: bold;
}

.confirmation-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
}

.confirmation-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    text-align: left;
}

.info-card {
    background: var(--background-white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.info-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.info-card p {
    color: var(--text-light);
    line-height: 1.6;
}

.info-card a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: bold;
}

.info-card a:hover {
    color: var(--secondary-color);
}

/* Responsive Confirmation */
@media (max-width: 768px) {
    .confirmation-header h1 {
        font-size: 2rem;
    }

    .confirmation-actions {
        flex-direction: column;
        align-items: center;
    }

    .confirmation-actions a {
        width: 100%;
        max-width: 300px;
        text-align: center;
    }

    .order-info {
        grid-template-columns: 1fr;
    }

    .confirmation-info {
        grid-template-columns: 1fr;
    }
}
