// Shopping cart functionality
class ShoppingCart {
    constructor() {
        this.items = this.loadCart();
        this.updateCartCount();
    }

    // Load cart from localStorage
    loadCart() {
        const savedCart = localStorage.getItem('luminaCart');
        return savedCart ? JSON.parse(savedCart) : [];
    }

    // Save cart to localStorage
    saveCart() {
        localStorage.setItem('luminaCart', JSON.stringify(this.items));
        this.updateCartCount();
    }

    // Add item to cart
    addItem(productId, quantity = 1) {
        const product = getProductById(productId);
        if (!product) return false;

        const existingItem = this.items.find(item => item.productId === productId);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.items.push({
                productId: productId,
                quantity: quantity,
                addedAt: new Date().toISOString()
            });
        }

        this.saveCart();
        this.showAddToCartMessage(product.name);
        return true;
    }

    // Remove item from cart
    removeItem(productId) {
        this.items = this.items.filter(item => item.productId !== productId);
        this.saveCart();
    }

    // Update item quantity
    updateQuantity(productId, quantity) {
        const item = this.items.find(item => item.productId === productId);
        if (item) {
            if (quantity <= 0) {
                this.removeItem(productId);
            } else {
                item.quantity = quantity;
                this.saveCart();
            }
        }
    }

    // Get cart items with product details
    getCartItems() {
        return this.items.map(item => {
            const product = getProductById(item.productId);
            return {
                ...item,
                product: product,
                subtotal: product ? product.price * item.quantity : 0
            };
        }).filter(item => item.product); // Filter out items with missing products
    }

    // Get cart total
    getTotal() {
        return this.getCartItems().reduce((total, item) => total + item.subtotal, 0);
    }

    // Get cart item count
    getItemCount() {
        return this.items.reduce((count, item) => count + item.quantity, 0);
    }

    // Clear cart
    clearCart() {
        this.items = [];
        this.saveCart();
    }

    // Update cart count display
    updateCartCount() {
        const cartCountElement = document.getElementById('cartCount');
        if (cartCountElement) {
            const count = this.getItemCount();
            cartCountElement.textContent = count;
            cartCountElement.style.display = count > 0 ? 'flex' : 'none';
        }
    }

    // Show add to cart message
    showAddToCartMessage(productName) {
        // Create and show a temporary message
        const message = document.createElement('div');
        message.className = 'cart-message';
        message.innerHTML = `
            <div class="cart-message-content">
                <span>✓ ${productName} added to cart!</span>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        document.body.appendChild(message);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (message.parentElement) {
                message.remove();
            }
        }, 3000);
    }
}

// Initialize cart
const cart = new ShoppingCart();

// Global functions for cart operations
function addToCart(productId, quantity = 1) {
    cart.addItem(productId, quantity);
}

function removeFromCart(productId) {
    cart.removeItem(productId);
    if (typeof renderCartItems === 'function') {
        renderCartItems();
    }
}

function updateCartQuantity(productId, quantity) {
    cart.updateQuantity(productId, parseInt(quantity));
    if (typeof renderCartItems === 'function') {
        renderCartItems();
    }
}

function clearCart() {
    if (confirm('Are you sure you want to clear your cart?')) {
        cart.clearCart();
        if (typeof renderCartItems === 'function') {
            renderCartItems();
        }
    }
}

// Cart page specific functions
function renderCartItems() {
    const cartItemsContainer = document.getElementById('cartItems');
    const cartSummary = document.getElementById('cartSummary');
    
    if (!cartItemsContainer) return;

    const cartItems = cart.getCartItems();
    
    if (cartItems.length === 0) {
        cartItemsContainer.innerHTML = `
            <div class="empty-cart">
                <h3>Your cart is empty</h3>
                <p>Discover our beautiful collection of handcrafted candles</p>
                <a href="products.html" class="cta-button">Shop Now</a>
            </div>
        `;
        if (cartSummary) {
            cartSummary.style.display = 'none';
        }
        return;
    }

    cartItemsContainer.innerHTML = cartItems.map(item => `
        <div class="cart-item">
            <div class="cart-item-image">
                <span style="font-size: 3rem;">${item.product.image}</span>
            </div>
            <div class="cart-item-details">
                <h3>${item.product.name}</h3>
                <p class="cart-item-price">$${item.product.price.toFixed(2)}</p>
                <p class="cart-item-category">${item.product.category}</p>
            </div>
            <div class="cart-item-quantity">
                <label for="quantity-${item.productId}">Quantity:</label>
                <input 
                    type="number" 
                    id="quantity-${item.productId}"
                    min="1" 
                    max="10" 
                    value="${item.quantity}"
                    onchange="updateCartQuantity(${item.productId}, this.value)"
                >
            </div>
            <div class="cart-item-subtotal">
                <span>$${item.subtotal.toFixed(2)}</span>
            </div>
            <div class="cart-item-remove">
                <button onclick="removeFromCart(${item.productId})" class="remove-btn">
                    Remove
                </button>
            </div>
        </div>
    `).join('');

    // Update cart summary
    if (cartSummary) {
        const total = cart.getTotal();
        const itemCount = cart.getItemCount();
        
        cartSummary.style.display = 'block';
        cartSummary.innerHTML = `
            <h3>Order Summary</h3>
            <div class="summary-line">
                <span>Items (${itemCount}):</span>
                <span>$${total.toFixed(2)}</span>
            </div>
            <div class="summary-line">
                <span>Shipping:</span>
                <span>$${total >= 50 ? '0.00' : '5.99'}</span>
            </div>
            <div class="summary-line total">
                <span>Total:</span>
                <span>$${(total + (total >= 50 ? 0 : 5.99)).toFixed(2)}</span>
            </div>
            ${total >= 50 ? '<p class="free-shipping">🎉 You qualify for free shipping!</p>' : 
              `<p class="shipping-notice">Add $${(50 - total).toFixed(2)} more for free shipping</p>`}
            <button class="checkout-btn" onclick="proceedToCheckout()">
                Proceed to Checkout
            </button>
            <button class="clear-cart-btn" onclick="clearCart()">
                Clear Cart
            </button>
        `;
    }
}

function proceedToCheckout() {
    if (cart.getItemCount() === 0) {
        alert('Your cart is empty!');
        return;
    }
    window.location.href = 'checkout.html';
}

// Initialize cart page if we're on it
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('cartItems')) {
        renderCartItems();
    }
});
