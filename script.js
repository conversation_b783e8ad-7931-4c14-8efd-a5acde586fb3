// Main JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize mobile menu
    initializeMobileMenu();
    
    // Load featured products on homepage
    if (document.getElementById('featuredProducts')) {
        loadFeaturedProducts();
    }
    
    // Initialize product page if we're on it
    if (document.getElementById('productsContainer')) {
        initializeProductsPage();
    }
    
    // Initialize product detail page if we're on it
    if (document.getElementById('productDetail')) {
        initializeProductDetailPage();
    }
    
    // Initialize checkout page if we're on it
    if (document.getElementById('checkoutForm')) {
        initializeCheckoutPage();
    }
    
    // Smooth scrolling for anchor links
    initializeSmoothScrolling();
}

// Mobile menu functionality
function initializeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            mobileMenuBtn.classList.toggle('active');
        });
        
        // Close menu when clicking on a link
        navMenu.addEventListener('click', function(e) {
            if (e.target.classList.contains('nav-link')) {
                navMenu.classList.remove('active');
                mobileMenuBtn.classList.remove('active');
            }
        });
    }
}

// Load featured products on homepage
function loadFeaturedProducts() {
    const featuredProducts = getFeaturedProducts();
    renderProductGrid(featuredProducts, 'featuredProducts');
}

// Products page initialization
function initializeProductsPage() {
    const urlParams = new URLSearchParams(window.location.search);
    const category = urlParams.get('category') || 'all';
    const searchTerm = urlParams.get('search') || '';
    
    // Set initial filter values
    if (document.getElementById('categoryFilter')) {
        document.getElementById('categoryFilter').value = category;
    }
    if (document.getElementById('searchInput')) {
        document.getElementById('searchInput').value = searchTerm;
    }
    
    // Load and display products
    loadProducts();
    
    // Initialize filter event listeners
    initializeFilters();
}

// Product filtering and loading
function loadProducts(page = 1) {
    const category = document.getElementById('categoryFilter')?.value || 'all';
    const priceRange = document.getElementById('priceFilter')?.value || 'all';
    const size = document.getElementById('sizeFilter')?.value || 'all';
    const searchTerm = document.getElementById('searchInput')?.value || '';
    
    const filteredProducts = filterProducts(category, priceRange, size, searchTerm);
    const paginatedData = paginateProducts(filteredProducts, page, 9);
    
    renderProductGrid(paginatedData.products, 'productsContainer');
    renderPagination(paginatedData, 'paginationContainer');
    
    // Update results count
    const resultsCount = document.getElementById('resultsCount');
    if (resultsCount) {
        resultsCount.textContent = `Showing ${paginatedData.products.length} of ${paginatedData.totalProducts} products`;
    }
}

// Initialize filter event listeners
function initializeFilters() {
    const filters = ['categoryFilter', 'priceFilter', 'sizeFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', () => loadProducts(1));
        }
    });
    
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => loadProducts(1), 300);
        });
    }
}

// Render pagination
function renderPagination(paginatedData, containerId) {
    const container = document.getElementById(containerId);
    if (!container || paginatedData.totalPages <= 1) {
        if (container) container.innerHTML = '';
        return;
    }
    
    let paginationHTML = '<div class="pagination">';
    
    // Previous button
    if (paginatedData.currentPage > 1) {
        paginationHTML += `<button onclick="loadProducts(${paginatedData.currentPage - 1})" class="pagination-btn">Previous</button>`;
    }
    
    // Page numbers
    for (let i = 1; i <= paginatedData.totalPages; i++) {
        const isActive = i === paginatedData.currentPage ? 'active' : '';
        paginationHTML += `<button onclick="loadProducts(${i})" class="pagination-btn ${isActive}">${i}</button>`;
    }
    
    // Next button
    if (paginatedData.currentPage < paginatedData.totalPages) {
        paginationHTML += `<button onclick="loadProducts(${paginatedData.currentPage + 1})" class="pagination-btn">Next</button>`;
    }
    
    paginationHTML += '</div>';
    container.innerHTML = paginationHTML;
}

// Product detail page initialization
function initializeProductDetailPage() {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');
    
    if (!productId) {
        window.location.href = 'products.html';
        return;
    }
    
    const product = getProductById(productId);
    if (!product) {
        window.location.href = 'products.html';
        return;
    }
    
    renderProductDetail(product);
}

// Render product detail
function renderProductDetail(product) {
    const container = document.getElementById('productDetail');
    if (!container) return;
    
    container.innerHTML = `
        <div class="product-detail-container">
            <div class="product-detail-image">
                <div class="product-image-large">
                    <span style="font-size: 8rem;">${product.image}</span>
                </div>
            </div>
            <div class="product-detail-info">
                <h1>${product.name}</h1>
                <p class="product-detail-price">$${product.price.toFixed(2)}</p>
                <p class="product-detail-description">${product.description}</p>
                
                <div class="product-details">
                    <h3>Product Details</h3>
                    <ul>
                        <li><strong>Burn Time:</strong> ${product.burnTime}</li>
                        <li><strong>Size:</strong> ${product.size}</li>
                        <li><strong>Category:</strong> ${product.category}</li>
                        <li><strong>Scent:</strong> ${product.scent}</li>
                    </ul>
                </div>
                
                <div class="product-ingredients">
                    <h3>Ingredients</h3>
                    <ul>
                        ${product.ingredients.map(ingredient => `<li>${ingredient}</li>`).join('')}
                    </ul>
                </div>
                
                <div class="product-actions">
                    <div class="quantity-selector">
                        <label for="quantity">Quantity:</label>
                        <input type="number" id="quantity" min="1" max="10" value="1">
                    </div>
                    <button class="add-to-cart-btn large" onclick="addProductToCart(${product.id})">
                        Add to Cart
                    </button>
                </div>
                
                <div class="product-navigation">
                    <a href="products.html" class="back-link">← Back to Products</a>
                </div>
            </div>
        </div>
    `;
}

// Add product to cart from detail page
function addProductToCart(productId) {
    const quantity = parseInt(document.getElementById('quantity').value) || 1;
    addToCart(productId, quantity);
}

// Checkout page initialization
function initializeCheckoutPage() {
    const cartItems = cart.getCartItems();
    
    if (cartItems.length === 0) {
        window.location.href = 'cart.html';
        return;
    }
    
    renderCheckoutSummary(cartItems);
    initializeCheckoutForm();
}

// Render checkout summary
function renderCheckoutSummary(cartItems) {
    const container = document.getElementById('checkoutSummary');
    if (!container) return;
    
    const total = cart.getTotal();
    const shipping = total >= 50 ? 0 : 5.99;
    const finalTotal = total + shipping;
    
    container.innerHTML = `
        <h3>Order Summary</h3>
        <div class="checkout-items">
            ${cartItems.map(item => `
                <div class="checkout-item">
                    <span>${item.product.name} × ${item.quantity}</span>
                    <span>$${item.subtotal.toFixed(2)}</span>
                </div>
            `).join('')}
        </div>
        <div class="checkout-totals">
            <div class="checkout-line">
                <span>Subtotal:</span>
                <span>$${total.toFixed(2)}</span>
            </div>
            <div class="checkout-line">
                <span>Shipping:</span>
                <span>$${shipping.toFixed(2)}</span>
            </div>
            <div class="checkout-line total">
                <span>Total:</span>
                <span>$${finalTotal.toFixed(2)}</span>
            </div>
        </div>
    `;
}

// Initialize checkout form
function initializeCheckoutForm() {
    const form = document.getElementById('checkoutForm');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        processOrder();
    });
}

// Process order
function processOrder() {
    // Simulate order processing
    const orderNumber = Math.random().toString(36).substr(2, 9).toUpperCase();
    
    // Store order details
    const orderData = {
        orderNumber: orderNumber,
        items: cart.getCartItems(),
        total: cart.getTotal() + (cart.getTotal() >= 50 ? 0 : 5.99),
        date: new Date().toISOString()
    };
    
    localStorage.setItem('lastOrder', JSON.stringify(orderData));
    
    // Clear cart
    cart.clearCart();
    
    // Redirect to confirmation
    window.location.href = 'confirmation.html';
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}
