<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Lumina Candles</title>
    <meta name="description" content="Complete your candle purchase with our secure checkout process.">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <a href="index.html" class="nav-logo">
                    <span class="logo-icon">🕯️</span>
                    Lumina Candles
                </a>
                
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="products.html" class="nav-link">Products</a></li>
                    <li><a href="index.html#about" class="nav-link">About</a></li>
                    <li><a href="index.html#contact" class="nav-link">Contact</a></li>
                </ul>
                
                <div class="nav-actions">
                    <button class="cart-btn" onclick="window.location.href='cart.html'">
                        <span class="cart-icon">🛒</span>
                        <span class="cart-count" id="cartCount">0</span>
                    </button>
                    <button class="mobile-menu-btn" id="mobileMenuBtn">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Checkout</h1>
            <p>Complete your order securely</p>
        </div>
    </section>

    <!-- Checkout Content -->
    <section class="checkout-section">
        <div class="container">
            <div class="checkout-container">
                <div class="checkout-form-section">
                    <form id="checkoutForm" class="checkout-form">
                        <!-- Customer Information -->
                        <div class="form-section">
                            <h3>Customer Information</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="firstName">First Name *</label>
                                    <input type="text" id="firstName" name="firstName" required>
                                </div>
                                <div class="form-group">
                                    <label for="lastName">Last Name *</label>
                                    <input type="text" id="lastName" name="lastName" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone">
                            </div>
                        </div>

                        <!-- Shipping Address -->
                        <div class="form-section">
                            <h3>Shipping Address</h3>
                            <div class="form-group">
                                <label for="address">Street Address *</label>
                                <input type="text" id="address" name="address" required>
                            </div>
                            <div class="form-group">
                                <label for="apartment">Apartment, Suite, etc.</label>
                                <input type="text" id="apartment" name="apartment">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="city">City *</label>
                                    <input type="text" id="city" name="city" required>
                                </div>
                                <div class="form-group">
                                    <label for="state">State *</label>
                                    <select id="state" name="state" required>
                                        <option value="">Select State</option>
                                        <option value="AL">Alabama</option>
                                        <option value="AK">Alaska</option>
                                        <option value="AZ">Arizona</option>
                                        <option value="AR">Arkansas</option>
                                        <option value="CA">California</option>
                                        <option value="CO">Colorado</option>
                                        <option value="CT">Connecticut</option>
                                        <option value="DE">Delaware</option>
                                        <option value="FL">Florida</option>
                                        <option value="GA">Georgia</option>
                                        <!-- Add more states as needed -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="zipCode">ZIP Code *</label>
                                    <input type="text" id="zipCode" name="zipCode" required>
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Options -->
                        <div class="form-section">
                            <h3>Shipping Options</h3>
                            <div class="shipping-options">
                                <label class="shipping-option">
                                    <input type="radio" name="shipping" value="standard" checked>
                                    <div class="shipping-details">
                                        <span class="shipping-name">Standard Shipping</span>
                                        <span class="shipping-time">5-7 business days</span>
                                        <span class="shipping-price">$5.99</span>
                                    </div>
                                </label>
                                <label class="shipping-option">
                                    <input type="radio" name="shipping" value="express">
                                    <div class="shipping-details">
                                        <span class="shipping-name">Express Shipping</span>
                                        <span class="shipping-time">2-3 business days</span>
                                        <span class="shipping-price">$12.99</span>
                                    </div>
                                </label>
                                <label class="shipping-option">
                                    <input type="radio" name="shipping" value="overnight">
                                    <div class="shipping-details">
                                        <span class="shipping-name">Overnight Shipping</span>
                                        <span class="shipping-time">1 business day</span>
                                        <span class="shipping-price">$24.99</span>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="form-section">
                            <h3>Payment Method</h3>
                            <div class="payment-options">
                                <label class="payment-option">
                                    <input type="radio" name="payment" value="credit" checked>
                                    <span>💳 Credit/Debit Card</span>
                                </label>
                                <label class="payment-option">
                                    <input type="radio" name="payment" value="paypal">
                                    <span>🅿️ PayPal</span>
                                </label>
                                <label class="payment-option">
                                    <input type="radio" name="payment" value="apple">
                                    <span>🍎 Apple Pay</span>
                                </label>
                            </div>
                            
                            <div id="creditCardFields" class="credit-card-fields">
                                <div class="form-group">
                                    <label for="cardNumber">Card Number *</label>
                                    <input type="text" id="cardNumber" name="cardNumber" placeholder="1234 5678 9012 3456">
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="expiryDate">Expiry Date *</label>
                                        <input type="text" id="expiryDate" name="expiryDate" placeholder="MM/YY">
                                    </div>
                                    <div class="form-group">
                                        <label for="cvv">CVV *</label>
                                        <input type="text" id="cvv" name="cvv" placeholder="123">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="cardName">Name on Card *</label>
                                    <input type="text" id="cardName" name="cardName">
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="place-order-btn">
                            Place Order
                        </button>
                    </form>
                </div>
                
                <div class="checkout-summary-section">
                    <div id="checkoutSummary" class="checkout-summary">
                        <!-- Order summary will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Lumina Candles</h3>
                    <p>Illuminating homes with premium handcrafted candles since 2020.</p>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <p>📧 <EMAIL></p>
                    <p>📞 (555) 123-4567</p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Lumina Candles. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="products.js"></script>
    <script src="cart.js"></script>
    <script src="script.js"></script>
</body>
</html>
