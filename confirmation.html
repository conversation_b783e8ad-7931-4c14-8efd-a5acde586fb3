<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - Lumina Candles</title>
    <meta name="description" content="Thank you for your order! Your candles are on their way.">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <a href="index.html" class="nav-logo">
                    <span class="logo-icon">🕯️</span>
                    Lumina Candles
                </a>
                
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="products.html" class="nav-link">Products</a></li>
                    <li><a href="index.html#about" class="nav-link">About</a></li>
                    <li><a href="index.html#contact" class="nav-link">Contact</a></li>
                </ul>
                
                <div class="nav-actions">
                    <button class="cart-btn" onclick="window.location.href='cart.html'">
                        <span class="cart-icon">🛒</span>
                        <span class="cart-count" id="cartCount">0</span>
                    </button>
                    <button class="mobile-menu-btn" id="mobileMenuBtn">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Confirmation Content -->
    <section class="confirmation-section">
        <div class="container">
            <div class="confirmation-container">
                <div class="confirmation-header">
                    <div class="success-icon">✅</div>
                    <h1>Order Confirmed!</h1>
                    <p>Thank you for your purchase. Your order has been successfully placed.</p>
                </div>
                
                <div class="order-details" id="orderDetails">
                    <!-- Order details will be loaded dynamically -->
                </div>
                
                <div class="confirmation-actions">
                    <a href="products.html" class="cta-button">Continue Shopping</a>
                    <a href="index.html" class="secondary-button">Back to Home</a>
                </div>
                
                <div class="confirmation-info">
                    <div class="info-card">
                        <h3>📧 Email Confirmation</h3>
                        <p>A confirmation email has been sent to your email address with order details and tracking information.</p>
                    </div>
                    
                    <div class="info-card">
                        <h3>🚚 Shipping Information</h3>
                        <p>Your candles will be carefully packaged and shipped within 1-2 business days. You'll receive tracking information once your order ships.</p>
                    </div>
                    
                    <div class="info-card">
                        <h3>💬 Customer Support</h3>
                        <p>If you have any questions about your order, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a> or call (555) 123-4567.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Lumina Candles</h3>
                    <p>Illuminating homes with premium handcrafted candles since 2020.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Instagram">📷</a>
                        <a href="#" aria-label="Facebook">📘</a>
                        <a href="#" aria-label="Pinterest">📌</a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="products.html">All Products</a></li>
                        <li><a href="index.html#about">About Us</a></li>
                        <li><a href="cart.html">Shopping Cart</a></li>
                        <li><a href="index.html#contact">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Customer Care</h4>
                    <ul>
                        <li><a href="#">Shipping Info</a></li>
                        <li><a href="#">Returns</a></li>
                        <li><a href="#">Size Guide</a></li>
                        <li><a href="#">Care Instructions</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <p>📧 <EMAIL></p>
                    <p>📞 (555) 123-4567</p>
                    <p>📍 123 Craft Street, Artisan City, AC 12345</p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Lumina Candles. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="products.js"></script>
    <script src="cart.js"></script>
    <script src="script.js"></script>
    <script>
        // Load order details when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const orderData = localStorage.getItem('lastOrder');
            
            if (!orderData) {
                // Redirect to home if no order data
                window.location.href = 'index.html';
                return;
            }
            
            const order = JSON.parse(orderData);
            renderOrderDetails(order);
        });
        
        function renderOrderDetails(order) {
            const container = document.getElementById('orderDetails');
            if (!container) return;
            
            const orderDate = new Date(order.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            container.innerHTML = `
                <div class="order-summary">
                    <h2>Order Summary</h2>
                    <div class="order-info">
                        <div class="order-number">
                            <strong>Order Number:</strong> #${order.orderNumber}
                        </div>
                        <div class="order-date">
                            <strong>Order Date:</strong> ${orderDate}
                        </div>
                        <div class="order-total">
                            <strong>Total:</strong> $${order.total.toFixed(2)}
                        </div>
                    </div>
                    
                    <div class="order-items">
                        <h3>Items Ordered</h3>
                        ${order.items.map(item => `
                            <div class="order-item">
                                <div class="item-image">
                                    <span style="font-size: 2rem;">${item.product.image}</span>
                                </div>
                                <div class="item-details">
                                    <div class="item-name">${item.product.name}</div>
                                    <div class="item-quantity">Quantity: ${item.quantity}</div>
                                    <div class="item-price">$${item.subtotal.toFixed(2)}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
