// Product data
const products = [
    {
        id: 1,
        name: "Lavender Dreams",
        price: 24.99,
        category: "floral",
        scent: "lavender",
        size: "medium",
        description: "A calming blend of French lavender and vanilla, perfect for relaxation and peaceful evenings.",
        image: "🌸",
        featured: true,
        burnTime: "45 hours",
        ingredients: ["Soy wax", "Lavender essential oil", "Vanilla extract", "Cotton wick"]
    },
    {
        id: 2,
        name: "Sandalwood Serenity",
        price: 29.99,
        category: "woody",
        scent: "sandalwood",
        size: "large",
        description: "Rich sandalwood with hints of cedar and amber, creating a warm and grounding atmosphere.",
        image: "🌲",
        featured: true,
        burnTime: "60 hours",
        ingredients: ["Soy wax", "Sandalwood essential oil", "Cedar oil", "Cotton wick"]
    },
    {
        id: 3,
        name: "Citrus Burst",
        price: 19.99,
        category: "citrus",
        scent: "orange",
        size: "small",
        description: "Energizing blend of sweet orange, lemon, and grapefruit to brighten any space.",
        image: "🍊",
        featured: true,
        burnTime: "30 hours",
        ingredients: ["Soy wax", "Orange essential oil", "Lemon oil", "Cotton wick"]
    },
    {
        id: 4,
        name: "Vanilla Comfort",
        price: 26.99,
        category: "vanilla",
        scent: "vanilla",
        size: "medium",
        description: "Pure Madagascar vanilla with subtle notes of caramel and cream.",
        image: "🍦",
        featured: false,
        burnTime: "50 hours",
        ingredients: ["Soy wax", "Vanilla extract", "Caramel essence", "Cotton wick"]
    },
    {
        id: 5,
        name: "Rose Garden",
        price: 32.99,
        category: "floral",
        scent: "rose",
        size: "large",
        description: "Elegant Bulgarian rose petals with a touch of jasmine and white tea.",
        image: "🌹",
        featured: false,
        burnTime: "65 hours",
        ingredients: ["Soy wax", "Rose essential oil", "Jasmine oil", "Cotton wick"]
    },
    {
        id: 6,
        name: "Pine Forest",
        price: 27.99,
        category: "woody",
        scent: "pine",
        size: "medium",
        description: "Fresh pine needles with eucalyptus and a hint of winter mint.",
        image: "🌲",
        featured: false,
        burnTime: "48 hours",
        ingredients: ["Soy wax", "Pine essential oil", "Eucalyptus oil", "Cotton wick"]
    },
    {
        id: 7,
        name: "Lemon Verbena",
        price: 22.99,
        category: "citrus",
        scent: "lemon",
        size: "small",
        description: "Crisp lemon verbena with fresh mint and a touch of green tea.",
        image: "🍋",
        featured: false,
        burnTime: "35 hours",
        ingredients: ["Soy wax", "Lemon verbena oil", "Mint oil", "Cotton wick"]
    },
    {
        id: 8,
        name: "Vanilla Spice",
        price: 28.99,
        category: "vanilla",
        scent: "vanilla",
        size: "medium",
        description: "Warm vanilla bean with cinnamon, nutmeg, and a hint of clove.",
        image: "🍦",
        featured: false,
        burnTime: "52 hours",
        ingredients: ["Soy wax", "Vanilla bean", "Cinnamon oil", "Cotton wick"]
    },
    {
        id: 9,
        name: "Jasmine Night",
        price: 31.99,
        category: "floral",
        scent: "jasmine",
        size: "large",
        description: "Intoxicating jasmine blossoms with white musk and moonflower.",
        image: "🌸",
        featured: false,
        burnTime: "58 hours",
        ingredients: ["Soy wax", "Jasmine essential oil", "White musk", "Cotton wick"]
    },
    {
        id: 10,
        name: "Cedar & Sage",
        price: 25.99,
        category: "woody",
        scent: "cedar",
        size: "medium",
        description: "Rustic cedar wood with white sage and a touch of bergamot.",
        image: "🌲",
        featured: false,
        burnTime: "46 hours",
        ingredients: ["Soy wax", "Cedar oil", "White sage", "Cotton wick"]
    },
    {
        id: 11,
        name: "Grapefruit Mint",
        price: 21.99,
        category: "citrus",
        scent: "grapefruit",
        size: "small",
        description: "Zesty pink grapefruit with cooling spearmint and lime zest.",
        image: "🍊",
        featured: false,
        burnTime: "32 hours",
        ingredients: ["Soy wax", "Grapefruit oil", "Spearmint oil", "Cotton wick"]
    },
    {
        id: 12,
        name: "French Vanilla",
        price: 30.99,
        category: "vanilla",
        scent: "vanilla",
        size: "large",
        description: "Luxurious French vanilla with bourbon and sweet cream notes.",
        image: "🍦",
        featured: false,
        burnTime: "62 hours",
        ingredients: ["Soy wax", "French vanilla", "Bourbon essence", "Cotton wick"]
    }
];

// Filter and search functions
function filterProducts(category = 'all', priceRange = 'all', size = 'all', searchTerm = '') {
    return products.filter(product => {
        const matchesCategory = category === 'all' || product.category === category;
        const matchesSize = size === 'all' || product.size === size;
        const matchesSearch = searchTerm === '' || 
            product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product.scent.toLowerCase().includes(searchTerm.toLowerCase());
        
        let matchesPrice = true;
        if (priceRange !== 'all') {
            switch (priceRange) {
                case 'under-25':
                    matchesPrice = product.price < 25;
                    break;
                case '25-30':
                    matchesPrice = product.price >= 25 && product.price <= 30;
                    break;
                case 'over-30':
                    matchesPrice = product.price > 30;
                    break;
            }
        }
        
        return matchesCategory && matchesSize && matchesPrice && matchesSearch;
    });
}

function getFeaturedProducts() {
    return products.filter(product => product.featured);
}

function getProductById(id) {
    return products.find(product => product.id === parseInt(id));
}

// Pagination
function paginateProducts(products, page = 1, itemsPerPage = 9) {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    
    return {
        products: products.slice(startIndex, endIndex),
        totalPages: Math.ceil(products.length / itemsPerPage),
        currentPage: page,
        totalProducts: products.length
    };
}

// Product rendering functions
function renderProductCard(product, showAddToCart = true) {
    return `
        <div class="product-card" onclick="viewProduct(${product.id})">
            <div class="product-image">
                <span style="font-size: 4rem;">${product.image}</span>
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-price">$${product.price.toFixed(2)}</p>
                ${showAddToCart ? `
                    <button class="add-to-cart-btn" onclick="event.stopPropagation(); addToCart(${product.id})">
                        Add to Cart
                    </button>
                ` : ''}
            </div>
        </div>
    `;
}

function renderProductGrid(products, containerId, showAddToCart = true) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    if (products.length === 0) {
        container.innerHTML = '<p class="no-products">No products found matching your criteria.</p>';
        return;
    }
    
    container.innerHTML = products.map(product => 
        renderProductCard(product, showAddToCart)
    ).join('');
}

// Navigation functions
function viewProduct(productId) {
    window.location.href = `product-detail.html?id=${productId}`;
}

function filterByCategory(category) {
    window.location.href = `products.html?category=${category}`;
}
